import { useRef, useCallback, useMemo } from 'react';
import { Platform, Dimensions } from 'react-native';
import {
  useSharedValue,
  useAnimatedStyle,
  runOnJS,
  withTiming,
  withSpring
} from 'react-native-reanimated';
import { Gesture } from 'react-native-gesture-handler';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Animation constants
const ROTATION_ANGLE = 12;
const VISIBLE_CARDS = 3;
const CARD_SCALE_DECREMENT = 0.03;
const CARD_POSITION_OFFSET = 15;
const UP_SWIPE_THRESHOLD = -15;
const SWIPE_SENSITIVITY = 0.005;

export const useSwipeGestures = ({
  items,
  currentIndex,
  onSwipeComplete,
  onResetAnimations,
  triggerLikeAnimation,
  triggerDislikeAnimation
}) => {
  // Animation values for the current top card
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const rotate = useSharedValue(0);

  // Stack animations for cards beneath (independent of top card)
  const cardAnimations = useRef(
    Array(VISIBLE_CARDS - 1).fill(0).map((_, index) => ({
      scale: useSharedValue(1 - CARD_SCALE_DECREMENT * (index + 1)),
      translateY: useSharedValue(CARD_POSITION_OFFSET * (index + 1)),
      opacity: useSharedValue(1),
    }))
  ).current;

  // Reset function - immediately resets the top card to default position
  // This is the KEY to fixing the animation glitch
  const resetCardAnimations = useCallback(() => {
    console.log('[useSwipeGestures] Resetting card animations');

    // IMMEDIATELY reset the top card animation values to default
    // This ensures the next card always starts from center position
    translateX.value = 0;
    translateY.value = 0;
    rotate.value = 0;

    // Reset stack cards to their default positions
    for (let i = 0; i < VISIBLE_CARDS - 1; i++) {
      const defaultScale = 1 - CARD_SCALE_DECREMENT * (i + 1);
      const defaultTranslateY = CARD_POSITION_OFFSET * (i + 1);

      // Use smooth spring animation for stack cards
      cardAnimations[i].scale.value = withSpring(defaultScale, {
        damping: 15,
        stiffness: 150,
        mass: 1
      });
      cardAnimations[i].translateY.value = withSpring(defaultTranslateY, {
        damping: 15,
        stiffness: 150,
        mass: 1
      });
      cardAnimations[i].opacity.value = withSpring(1, {
        damping: 15,
        stiffness: 150,
        mass: 1
      });
    }
  }, [translateX, translateY, rotate, cardAnimations]);

  // Process swipe completion - animation values are already reset in worklet context
  const processSwipeCompletion = useCallback((direction, swipedItem) => {
    console.log(`[useSwipeGestures] Processing swipe completion: ${direction} - animation values already reset`);

    // Call the completion handler (this will update currentIndex)
    // Animation values were already reset in the animation completion callback
    if (onSwipeComplete) {
      onSwipeComplete(direction, swipedItem);
    }

    // Reset stack animations after currentIndex update
    setTimeout(() => {
      resetCardAnimations();
    }, 16);
  }, [onSwipeComplete, resetCardAnimations]);

  // Create pan gesture - simple and clean
  const panGesture = useMemo(() => Gesture.Pan()
    .onStart(() => {
      'worklet';
      // Start from current position (should always be 0,0,0 for new cards)
      const startX = translateX.value;
      const startY = translateY.value;

      translateX.value = startX;
      translateY.value = startY;

      if (Platform.OS === 'android') {
        rotate.value = 0;
      }
    })
    .enabled(currentIndex < items.length)
    .minDistance(Platform.OS === 'android' ? 5 : 0)
    .onUpdate((event) => {
      'worklet';
      const { translationX, translationY } = event;

      // Determine primary swipe direction
      let direction;
      const directionThreshold = Platform.OS === 'android' ? 1.2 : 1.0;

      if (Math.abs(translationX) > Math.abs(translationY) * directionThreshold) {
        direction = 'horizontal';
      } else if (translationY < 0) {
        direction = 'up';
      } else {
        direction = 'down';
      }

      // Update card position based on gesture
      if (direction === 'horizontal') {
        translateX.value = translationX;
        translateY.value = 0;

        // Calculate rotation based on horizontal movement
        const rotationProgress = Math.max(-1, Math.min(1, translationX / (SCREEN_WIDTH / 2)));
        rotate.value = rotationProgress * ROTATION_ANGLE;
      } else if (direction === 'up') {
        translateY.value = Math.min(0, translationY);
        translateX.value = 0;
        rotate.value = 0;
      } else {
        // Down or insufficient movement - reset to center
        translateY.value = 0;
        translateX.value = 0;
        rotate.value = 0;
        return;
      }

      // Calculate swipe progress for stack animation preview
      let swipeProgress;
      if (direction === 'horizontal') {
        swipeProgress = Math.min(Math.abs(translationX) / (SCREEN_WIDTH * 0.5), 1);
      } else if (direction === 'up') {
        swipeProgress = Math.min(Math.abs(translationY) / 100, 1);
      } else {
        swipeProgress = 0;
      }

      // Preview stack animation during swipe (cards beneath move up slightly)
      if (swipeProgress > 0.01) {
        for (let i = 0; i < VISIBLE_CARDS - 1; i++) {
          // Calculate target positions (where cards will be after swipe)
          const currentScale = 1 - CARD_SCALE_DECREMENT * (i + 1);
          const targetScale = 1 - CARD_SCALE_DECREMENT * i;
          const currentTranslateY = CARD_POSITION_OFFSET * (i + 1);
          const targetTranslateY = CARD_POSITION_OFFSET * i;

          // Interpolate between current and target positions based on swipe progress
          cardAnimations[i].scale.value = currentScale + (targetScale - currentScale) * swipeProgress;
          cardAnimations[i].translateY.value = currentTranslateY + (targetTranslateY - currentTranslateY) * swipeProgress;
          cardAnimations[i].opacity.value = 1;
        }
      }
    })
    .onEnd((event) => {
      'worklet';
      try {
        // Validate current state
        if (currentIndex < 0 || currentIndex >= items.length) {
          console.warn(`[useSwipeGestures] Invalid currentIndex (${currentIndex}) in onEnd. Resetting.`);
          runOnJS(resetCardAnimations)();
          return;
        }

        const { translationX, translationY } = event;
        const swipedItem = items[currentIndex];

        if (!swipedItem) {
          console.warn(`[useSwipeGestures] swipedItem is null/undefined in onEnd for index ${currentIndex}. Resetting.`);
          runOnJS(resetCardAnimations)();
          return;
        }

        // Determine primary swipe direction
        let direction;
        if (Math.abs(translationX) > Math.abs(translationY)) {
          direction = 'horizontal';
        } else if (translationY < 0) {
          direction = 'up';
        } else {
          direction = 'down';
        }

        // Handle upward swipe (add to cart)
        if (direction === 'up' && translationY < UP_SWIPE_THRESHOLD) {
          console.log('[useSwipeGestures] Upward swipe detected - adding to cart');

          const upSwipeDuration = 300;
          const targetY = -SCREEN_HEIGHT * 1.5;

          // Animate card upward and off screen
          translateY.value = withTiming(targetY, { duration: upSwipeDuration }, (isFinished) => {
            if (isFinished) {
              // CRITICAL: Reset values immediately in worklet context before calling completion
              translateX.value = 0;
              translateY.value = 0;
              rotate.value = 0;
              runOnJS(processSwipeCompletion)('up', swipedItem);
            } else {
              runOnJS(resetCardAnimations)();
            }
          });

          // Reset X position and rotation during upward swipe
          translateX.value = withTiming(0, { duration: upSwipeDuration });
          rotate.value = withTiming(0, { duration: upSwipeDuration });

          return;
        }

        // Handle horizontal swipe (like/dislike)
        if (direction === 'horizontal') {
          const sensitiveThreshold = SCREEN_WIDTH * SWIPE_SENSITIVITY;
          if (Math.abs(translationX) > sensitiveThreshold) {
            const swipeDirection = translationX > 0 ? 'right' : 'left';
            const targetX = (swipeDirection === 'right' ? 1 : -1) * SCREEN_WIDTH * 1.5;
            const swipeDuration = 300;

            console.log(`[useSwipeGestures] ${swipeDirection} swipe detected`);

            // Animate card horizontally off screen
            translateX.value = withTiming(targetX, { duration: swipeDuration }, (isFinished) => {
              if (isFinished) {
                // CRITICAL: Reset values immediately in worklet context before calling completion
                translateX.value = 0;
                translateY.value = 0;
                rotate.value = 0;
                runOnJS(processSwipeCompletion)(swipeDirection, swipedItem);
              } else {
                runOnJS(resetCardAnimations)();
              }
            });

            // Add final rotation and reset Y position
            rotate.value = withTiming((swipeDirection === 'right' ? 1 : -1) * 30, { duration: swipeDuration });
            translateY.value = withTiming(0, { duration: swipeDuration });
            return;
          }
        }

        // No significant swipe detected - return to center
        console.log('[useSwipeGestures] No significant swipe detected. Returning to center.');
        runOnJS(resetCardAnimations)();

      } catch (err) {
        console.error("[useSwipeGestures] Error in swipe onEnd:", err);
        runOnJS(resetCardAnimations)();
      }
    }), [currentIndex, items.length, processSwipeCompletion, resetCardAnimations]);

  // Animated style for the top card with safety check
  const topCardStyle = useAnimatedStyle(() => {
    // Add safety check to ensure values are always valid
    const safeTranslateX = translateX.value || 0;
    const safeTranslateY = translateY.value || 0;
    const safeRotate = rotate.value || 0;

    return {
      transform: [
        { translateX: safeTranslateX },
        { translateY: safeTranslateY },
        { rotate: `${safeRotate}deg` },
      ],
    };
  });

  return {
    panGesture,
    topCardStyle,
    resetCardAnimations,
    cardAnimations
  };
};
