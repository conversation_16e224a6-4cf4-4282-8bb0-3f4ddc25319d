import { useRef, useCallback, useMemo } from 'react';
import { Platform, Dimensions } from 'react-native';
import {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  runOnJS,
  withTiming
} from 'react-native-reanimated';
import { Gesture } from 'react-native-gesture-handler';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const ROTATION_ANGLE = 12;
const VISIBLE_CARDS = 3;
const CARD_SCALE_DECREMENT = 0.03;
const CARD_POSITION_OFFSET = 15;
const UP_SWIPE_THRESHOLD = -15;
const SWIPE_SENSITIVITY = 0.005;

export const useSwipeGestures = ({
  items,
  currentIndex,
  onSwipeComplete,
  onResetAnimations,
  triggerLikeAnimation,
  triggerDislikeAnimation
}) => {
  // Animation values
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const rotate = useSharedValue(0);

  // Card animations for stack effect
  const cardAnimations = useRef(
    Array(VISIBLE_CARDS - 1).fill(0).map((_, index) => ({
      scale: useSharedValue(1 - CARD_SCALE_DECREMENT * (index + 1)),
      translateY: useSharedValue(CARD_POSITION_OFFSET * (index + 1)),
      opacity: useSharedValue(1),
    }))
  ).current;

  // Reset card animations immediately (for new top card)
  const resetTopCardAnimations = useCallback(() => {
    try {
      console.log('[useSwipeGestures] Resetting top card animations immediately');

      // Reset top card animations immediately without timing animation
      // This prevents the new top card from inheriting the previous card's position
      translateX.value = 0;
      translateY.value = 0;
      rotate.value = 0;
    } catch (error) {
      console.error("[useSwipeGestures] Error in resetTopCardAnimations:", error);
      // Fallback: set values directly
      translateX.value = 0;
      translateY.value = 0;
      rotate.value = 0;
    }
  }, [translateX, translateY, rotate]);

  // Reset card stack animations (for cards beneath)
  const resetStackAnimations = useCallback(() => {
    try {
      console.log('[useSwipeGestures] Resetting stack animations');
      const consistentTimingConfig = { duration: 300 };

      // Reset cards beneath to their new positions after a card is swiped
      for (let i = 0; i < VISIBLE_CARDS - 1; i++) {
        // After swipe: cardAnimations[0] should be at position 1, cardAnimations[1] at position 2, etc.
        const newScale = 1 - CARD_SCALE_DECREMENT * (i + 1);
        const newTranslateY = CARD_POSITION_OFFSET * (i + 1);

        cardAnimations[i].scale.value = withTiming(newScale, consistentTimingConfig);
        cardAnimations[i].translateY.value = withTiming(newTranslateY, consistentTimingConfig);
        cardAnimations[i].opacity.value = withTiming(1, consistentTimingConfig);
      }
    } catch (error) {
      console.error("[useSwipeGestures] Error in resetStackAnimations:", error);
      // Fallback: set values directly without animation
      for (let i = 0; i < VISIBLE_CARDS - 1; i++) {
        cardAnimations[i].scale.value = 1 - CARD_SCALE_DECREMENT * (i + 1);
        cardAnimations[i].translateY.value = CARD_POSITION_OFFSET * (i + 1);
        cardAnimations[i].opacity.value = 1;
      }
    }
  }, [cardAnimations]);

  // Combined reset function for external use (maintains backward compatibility)
  const resetCardAnimations = useCallback(() => {
    resetTopCardAnimations();
    resetStackAnimations();
  }, [resetTopCardAnimations, resetStackAnimations]);

  // Process swipe completion
  const processSwipeCompletion = useCallback((direction, swipedItem) => {
    // Immediately reset top card animations to prevent new card from inheriting position
    // This is the key fix: reset happens synchronously before new card becomes active
    resetTopCardAnimations();

    // Trigger animations based on swipe direction
    // Removed:
    // if (direction === 'right' && triggerLikeAnimation) {
    //   triggerLikeAnimation();
    // } else if (direction === 'left' && triggerDislikeAnimation) {
    //   triggerDislikeAnimation();
    // }
    // Note: Cart animation (direction === 'up') is handled by addToCart in useCartManager

    if (onSwipeComplete) {
      onSwipeComplete(direction, swipedItem);
    }

    // Reset stack animations after a short delay to allow smooth transition
    setTimeout(() => {
      resetStackAnimations();
    }, 50);
  }, [onSwipeComplete, resetTopCardAnimations, resetStackAnimations]);

  // Create pan gesture - recreate when currentIndex or items change
  const panGesture = useMemo(() => Gesture.Pan()
    .onStart(() => {
      'worklet';
      const startX = translateX.value;
      const startY = translateY.value;

      translateX.value = startX;
      translateY.value = startY;

      if (Platform.OS === 'android') {
        rotate.value = 0;
      }
    })
    .enabled(currentIndex < items.length)
    .minDistance(Platform.OS === 'android' ? 5 : 0)
    .onUpdate((event) => {
      'worklet';
      const { translationX, translationY } = event;

      // Determine primary direction
      let direction;
      const directionThreshold = Platform.OS === 'android' ? 1.2 : 1.0;

      if (Math.abs(translationX) > Math.abs(translationY) * directionThreshold) {
        direction = 'horizontal';
      } else if (translationY < 0) {
        direction = 'up';
      } else {
        direction = 'down';
      }

      // Handle movement based on direction
      if (direction === 'horizontal') {
        translateX.value = translationX;
        translateY.value = 0;

        // Calculate rotation manually to avoid interpolate warnings
        const rotationProgress = Math.max(-1, Math.min(1, translationX / (SCREEN_WIDTH / 2)));
        rotate.value = rotationProgress * ROTATION_ANGLE;
      } else if (direction === 'up') {
        translateY.value = Math.min(0, translationY);
        translateX.value = 0;
      } else {
        translateY.value = 0;
        translateX.value = 0;
        return;
      }

      // Calculate animation progress for cards beneath
      let swipeProgress;
      if (direction === 'horizontal') {
        swipeProgress = Math.min(Math.abs(translationX) / (SCREEN_WIDTH * 0.5), 1);
      } else if (direction === 'up') {
        swipeProgress = Math.min(Math.abs(translationY) / 100, 1);
      } else {
        swipeProgress = 0;
      }

      // Animate cards beneath with throttling to reduce warnings
      if (swipeProgress > 0.01) { // Only animate if there's meaningful progress
        for (let i = 0; i < VISIBLE_CARDS - 1; i++) {
          // cardAnimations[i] represents the card at position (i+1) moving to position i
          const scaleFrom = 1 - CARD_SCALE_DECREMENT * (i + 1);
          const scaleTo = 1 - CARD_SCALE_DECREMENT * i;
          const translateFrom = CARD_POSITION_OFFSET * (i + 1);
          const translateTo = CARD_POSITION_OFFSET * i;

          cardAnimations[i].scale.value = scaleFrom + (scaleTo - scaleFrom) * swipeProgress;
          cardAnimations[i].translateY.value = translateFrom + (translateTo - translateFrom) * swipeProgress;
          cardAnimations[i].opacity.value = 1;
        }
      }
    })
    .onEnd((event) => {
      'worklet';
      try {
        if (currentIndex < 0 || currentIndex >= items.length) {
          console.warn(`[useSwipeGestures] Invalid currentIndex (${currentIndex}) in onEnd. Resetting.`);
          runOnJS(resetTopCardAnimations)();
          return;
        }

        const { translationX, translationY } = event;
        const swipedItem = items[currentIndex];

        if (!swipedItem) {
          console.warn(`[useSwipeGestures] swipedItem is null/undefined in onEnd for index ${currentIndex}. Resetting.`);
          runOnJS(resetTopCardAnimations)();
          return;
        }

        // Determine primary direction
        let direction;
        if (Math.abs(translationX) > Math.abs(translationY)) {
          direction = 'horizontal';
        } else if (translationY < 0) {
          direction = 'up';
        } else {
          direction = 'down';
        }        // Handle upward swipe
        if (direction === 'up' && translationY < UP_SWIPE_THRESHOLD) {
          console.log('[useSwipeGestures] Upward swipe detected');

          // Note: Cart animation will be triggered by addToCart in useCartManager
          // This prevents double animation triggers

          const upSwipeDuration = 300; // Consistent with reset duration
          const targetY = -SCREEN_HEIGHT * 1.5;

          translateY.value = withTiming(targetY, { duration: upSwipeDuration }, (isFinished) => {
            if (isFinished) {
              runOnJS(processSwipeCompletion)('up', swipedItem);
            } else {
              runOnJS(resetTopCardAnimations)();
            }
          });

          translateX.value = withTiming(0, { duration: upSwipeDuration });
          rotate.value = withTiming(0, { duration: upSwipeDuration });

          return;
        }

        // Handle horizontal swipe
        if (direction === 'horizontal') {
          const sensitiveThreshold = SCREEN_WIDTH * SWIPE_SENSITIVITY;
          if (Math.abs(translationX) > sensitiveThreshold) {
            const swipeDirection = translationX > 0 ? 'right' : 'left';
            const targetX = (swipeDirection === 'right' ? 1 : -1) * SCREEN_WIDTH * 1.5;
            const swipeDuration = 300; // Consistent with reset duration

            translateX.value = withTiming(targetX, { duration: swipeDuration }, (isFinished) => {
              if (isFinished) {
                runOnJS(processSwipeCompletion)(swipeDirection, swipedItem);
              } else {
                runOnJS(resetTopCardAnimations)();
              }
            });

            rotate.value = withTiming((swipeDirection === 'right' ? 1 : -1) * 30, { duration: swipeDuration });
            translateY.value = withTiming(0, { duration: swipeDuration });
            return;
          }
        }

        // No significant swipe detected
        console.log('[useSwipeGestures] No significant swipe detected. Resetting.');
        runOnJS(resetTopCardAnimations)();

      } catch (err) {
        console.error("[useSwipeGestures] Error in swipe onEnd:", err);
        runOnJS(resetTopCardAnimations)();
      }
    }), [currentIndex, items.length, processSwipeCompletion, resetCardAnimations]);

  // Animated style for top card
  const topCardStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { rotate: `${rotate.value}deg` },
    ],
  }));

  return {
    panGesture,
    topCardStyle,
    resetCardAnimations,
    cardAnimations
  };
};
