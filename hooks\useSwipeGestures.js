import { useCallback, useMemo } from 'react';
import { Dimensions } from 'react-native';
import {
  useSharedValue,
  useAnimatedStyle,
  runOnJS,
  withTiming
} from 'react-native-reanimated';
import { Gesture } from 'react-native-gesture-handler';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// Simple constants
const SWIPE_THRESHOLD = SCREEN_WIDTH * 0.25;
const ROTATION_FACTOR = 0.1;

export const useSwipeGestures = ({
  items,
  currentIndex,
  onSwipeComplete
}) => {
  // Simple animation values
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const rotate = useSharedValue(0);

  // Simple completion handler
  const processSwipeCompletion = useCallback((direction, swipedItem) => {
    console.log(`[useSwipeGestures] Processing swipe completion: ${direction}`);

    // Call the completion handler first
    if (onSwipeComplete) {
      onSwipeComplete(direction, swipedItem);
    }

    // Reset animation values after completion
    setTimeout(() => {
      translateX.value = 0;
      translateY.value = 0;
      rotate.value = 0;
    }, 50);
  }, [onSwipeComplete, translateX, translateY, rotate]);

  // Simple pan gesture
  const panGesture = useMemo(() => {
    return Gesture.Pan()
      .onUpdate((event) => {
        const { translationX, translationY } = event;

        // Update position during drag
        translateX.value = translationX;
        translateY.value = translationY;

        // Simple rotation based on horizontal movement
        rotate.value = translationX * ROTATION_FACTOR;
      })
      .onEnd((event) => {
        const { translationX, translationY } = event;

        // Get current item
        const currentItem = items[currentIndex];
        if (!currentItem) {
          translateX.value = withTiming(0);
          translateY.value = withTiming(0);
          rotate.value = withTiming(0);
          return;
        }

        // Simple swipe detection
        const absX = Math.abs(translationX);
        const absY = Math.abs(translationY);

        // Determine direction
        let direction = null;
        if (absX > SWIPE_THRESHOLD && absX > absY) {
          direction = translationX > 0 ? 'right' : 'left';
        } else if (absY > SWIPE_THRESHOLD && absY > absX && translationY < 0) {
          direction = 'up';
        }

        // If no swipe detected, return to center
        if (!direction) {
          translateX.value = withTiming(0, { duration: 200 });
          translateY.value = withTiming(0, { duration: 200 });
          rotate.value = withTiming(0, { duration: 200 });
          return;
        }

        console.log(`[useSwipeGestures] ${direction} swipe detected`);

        // Animate card off screen based on direction
        let targetX = 0, targetY = 0;
        if (direction === 'right') {
          targetX = SCREEN_WIDTH * 1.5;
        } else if (direction === 'left') {
          targetX = -SCREEN_WIDTH * 1.5;
        } else if (direction === 'up') {
          targetY = -SCREEN_WIDTH * 1.5;
        }

        // Simple animation off screen
        translateX.value = withTiming(targetX, { duration: 300 }, (isFinished) => {
          if (isFinished) {
            runOnJS(processSwipeCompletion)(direction, currentItem);
          }
        });

        if (direction === 'up') {
          translateY.value = withTiming(targetY, { duration: 300 });
        }
      });
  }, [items, currentIndex, translateX, translateY, rotate, processSwipeCompletion]);

  // Simple animated style
  const topCardStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { rotate: `${rotate.value}deg` },
    ],
  }));

  // Simple reset function
  const resetCardAnimations = useCallback(() => {
    console.log('[useSwipeGestures] Resetting card animations');
    translateX.value = 0;
    translateY.value = 0;
    rotate.value = 0;
  }, [translateX, translateY, rotate]);

  return {
    panGesture,
    topCardStyle,
    resetCardAnimations
  };
};
