import { useRef, useCallback, useMemo, useEffect } from 'react';
import { Platform, Dimensions } from 'react-native';
import {
  useSharedValue,
  useAnimatedStyle,
  runOnJS,
  withTiming,
  withSpring
} from 'react-native-reanimated';
import { Gesture } from 'react-native-gesture-handler';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Animation constants
const ROTATION_ANGLE = 12;
const VISIBLE_CARDS = 3;
const CARD_SCALE_DECREMENT = 0.03;
const CARD_POSITION_OFFSET = 15;
const UP_SWIPE_THRESHOLD = -15;
const SWIPE_SENSITIVITY = 0.005;

// Animation timing configurations
const SWIPE_ANIMATION_CONFIG = { duration: 300 };
const STACK_ANIMATION_CONFIG = {
  damping: 15,
  stiffness: 150,
  mass: 1,
  overshootClamping: true
};

export const useSwipeGestures = ({
  items,
  currentIndex,
  onSwipeComplete,
  onResetAnimations,
  triggerLikeAnimation,
  triggerDislikeAnimation
}) => {
  // Core animation values for the active (top) card only
  // These values are NEVER reused - they're reset immediately after each swipe
  const activeCardTranslateX = useSharedValue(0);
  const activeCardTranslateY = useSharedValue(0);
  const activeCardRotate = useSharedValue(0);

  // Stack animation values for cards beneath (these animate smoothly)
  const stackAnimations = useRef(
    Array(VISIBLE_CARDS - 1).fill(0).map((_, index) => ({
      scale: useSharedValue(1 - CARD_SCALE_DECREMENT * (index + 1)),
      translateY: useSharedValue(CARD_POSITION_OFFSET * (index + 1)),
      opacity: useSharedValue(1),
    }))
  ).current;

  // Track if we're currently in a swipe animation to prevent interference
  const isSwipeInProgress = useRef(false);

  // Immediately reset active card to default position (called after swipe completes)
  const resetActiveCard = useCallback(() => {
    console.log('[useSwipeGestures] Resetting active card to default position');

    // Immediately reset to default position - no animation needed
    // This ensures the next card starts from the correct position
    activeCardTranslateX.value = 0;
    activeCardTranslateY.value = 0;
    activeCardRotate.value = 0;

    // Clear the swipe progress flag
    isSwipeInProgress.current = false;
  }, [activeCardTranslateX, activeCardTranslateY, activeCardRotate]);

  // Animate stack cards to their new positions (smooth transition)
  const animateStackToNewPositions = useCallback(() => {
    console.log('[useSwipeGestures] Animating stack to new positions');

    try {
      // Animate each card in the stack to move up one position
      for (let i = 0; i < VISIBLE_CARDS - 1; i++) {
        const targetScale = 1 - CARD_SCALE_DECREMENT * (i + 1);
        const targetTranslateY = CARD_POSITION_OFFSET * (i + 1);

        // Use spring animation for smooth, natural movement
        stackAnimations[i].scale.value = withSpring(targetScale, STACK_ANIMATION_CONFIG);
        stackAnimations[i].translateY.value = withSpring(targetTranslateY, STACK_ANIMATION_CONFIG);
        stackAnimations[i].opacity.value = withSpring(1, STACK_ANIMATION_CONFIG);
      }
    } catch (error) {
      console.error("[useSwipeGestures] Error in animateStackToNewPositions:", error);
      // Fallback: set values directly
      for (let i = 0; i < VISIBLE_CARDS - 1; i++) {
        stackAnimations[i].scale.value = 1 - CARD_SCALE_DECREMENT * (i + 1);
        stackAnimations[i].translateY.value = CARD_POSITION_OFFSET * (i + 1);
        stackAnimations[i].opacity.value = 1;
      }
    }
  }, [stackAnimations]);

  // Combined reset function for external use (maintains backward compatibility)
  const resetCardAnimations = useCallback(() => {
    resetActiveCard();
    animateStackToNewPositions();
  }, [resetActiveCard, animateStackToNewPositions]);

  // Process swipe completion with proper sequencing
  const processSwipeCompletion = useCallback((direction, swipedItem) => {
    console.log(`[useSwipeGestures] Processing swipe completion: ${direction}`);

    // Step 1: Immediately reset active card to prevent new card from inheriting position
    // This is the KEY FIX - happens synchronously before new card becomes active
    resetActiveCard();

    // Step 2: Call the completion handler (this updates currentIndex)
    if (onSwipeComplete) {
      onSwipeComplete(direction, swipedItem);
    }

    // Step 3: Animate stack cards to new positions after a brief delay
    // This allows the new card to become active before stack animation starts
    setTimeout(() => {
      animateStackToNewPositions();
    }, 16); // One frame delay for smooth transition
  }, [onSwipeComplete, resetActiveCard, animateStackToNewPositions]);

  // Create pan gesture with proper active card animation
  const panGesture = useMemo(() => Gesture.Pan()
    .onStart(() => {
      'worklet';
      // Mark that a swipe is in progress
      isSwipeInProgress.current = true;

      // Ensure we start from current position (should be 0,0,0 for new cards)
      const startX = activeCardTranslateX.value;
      const startY = activeCardTranslateY.value;

      activeCardTranslateX.value = startX;
      activeCardTranslateY.value = startY;

      if (Platform.OS === 'android') {
        activeCardRotate.value = 0;
      }
    })
    .enabled(currentIndex < items.length && !isSwipeInProgress.current)
    .minDistance(Platform.OS === 'android' ? 5 : 0)
    .onUpdate((event) => {
      'worklet';
      const { translationX, translationY } = event;

      // Determine primary swipe direction
      let direction;
      const directionThreshold = Platform.OS === 'android' ? 1.2 : 1.0;

      if (Math.abs(translationX) > Math.abs(translationY) * directionThreshold) {
        direction = 'horizontal';
      } else if (translationY < 0) {
        direction = 'up';
      } else {
        direction = 'down';
      }

      // Update active card position based on gesture
      if (direction === 'horizontal') {
        activeCardTranslateX.value = translationX;
        activeCardTranslateY.value = 0;

        // Calculate rotation based on horizontal movement
        const rotationProgress = Math.max(-1, Math.min(1, translationX / (SCREEN_WIDTH / 2)));
        activeCardRotate.value = rotationProgress * ROTATION_ANGLE;
      } else if (direction === 'up') {
        activeCardTranslateY.value = Math.min(0, translationY);
        activeCardTranslateX.value = 0;
        activeCardRotate.value = 0;
      } else {
        // Down or insufficient movement - reset to center
        activeCardTranslateY.value = 0;
        activeCardTranslateX.value = 0;
        activeCardRotate.value = 0;
        return;
      }

      // Calculate swipe progress for stack animation preview
      let swipeProgress;
      if (direction === 'horizontal') {
        swipeProgress = Math.min(Math.abs(translationX) / (SCREEN_WIDTH * 0.5), 1);
      } else if (direction === 'up') {
        swipeProgress = Math.min(Math.abs(translationY) / 100, 1);
      } else {
        swipeProgress = 0;
      }

      // Preview stack animation during swipe (cards beneath move up slightly)
      if (swipeProgress > 0.01) {
        for (let i = 0; i < VISIBLE_CARDS - 1; i++) {
          // Calculate target positions (where cards will be after swipe)
          const currentScale = 1 - CARD_SCALE_DECREMENT * (i + 1);
          const targetScale = 1 - CARD_SCALE_DECREMENT * i;
          const currentTranslateY = CARD_POSITION_OFFSET * (i + 1);
          const targetTranslateY = CARD_POSITION_OFFSET * i;

          // Interpolate between current and target positions based on swipe progress
          stackAnimations[i].scale.value = currentScale + (targetScale - currentScale) * swipeProgress;
          stackAnimations[i].translateY.value = currentTranslateY + (targetTranslateY - currentTranslateY) * swipeProgress;
          stackAnimations[i].opacity.value = 1;
        }
      }
    })
    .onEnd((event) => {
      'worklet';
      try {
        // Validate current state
        if (currentIndex < 0 || currentIndex >= items.length) {
          console.warn(`[useSwipeGestures] Invalid currentIndex (${currentIndex}) in onEnd. Resetting.`);
          runOnJS(resetActiveCard)();
          return;
        }

        const { translationX, translationY } = event;
        const swipedItem = items[currentIndex];

        if (!swipedItem) {
          console.warn(`[useSwipeGestures] swipedItem is null/undefined in onEnd for index ${currentIndex}. Resetting.`);
          runOnJS(resetActiveCard)();
          return;
        }

        // Determine primary swipe direction
        let direction;
        if (Math.abs(translationX) > Math.abs(translationY)) {
          direction = 'horizontal';
        } else if (translationY < 0) {
          direction = 'up';
        } else {
          direction = 'down';
        }

        // Handle upward swipe (add to cart)
        if (direction === 'up' && translationY < UP_SWIPE_THRESHOLD) {
          console.log('[useSwipeGestures] Upward swipe detected - adding to cart');

          const upSwipeDuration = 300;
          const targetY = -SCREEN_HEIGHT * 1.5;

          // Animate card upward and off screen
          activeCardTranslateY.value = withTiming(targetY, { duration: upSwipeDuration }, (isFinished) => {
            if (isFinished) {
              runOnJS(processSwipeCompletion)('up', swipedItem);
            } else {
              runOnJS(resetActiveCard)();
            }
          });

          // Reset X position and rotation during upward swipe
          activeCardTranslateX.value = withTiming(0, { duration: upSwipeDuration });
          activeCardRotate.value = withTiming(0, { duration: upSwipeDuration });

          return;
        }

        // Handle horizontal swipe (like/dislike)
        if (direction === 'horizontal') {
          const sensitiveThreshold = SCREEN_WIDTH * SWIPE_SENSITIVITY;
          if (Math.abs(translationX) > sensitiveThreshold) {
            const swipeDirection = translationX > 0 ? 'right' : 'left';
            const targetX = (swipeDirection === 'right' ? 1 : -1) * SCREEN_WIDTH * 1.5;
            const swipeDuration = 300;

            console.log(`[useSwipeGestures] ${swipeDirection} swipe detected`);

            // Animate card horizontally off screen
            activeCardTranslateX.value = withTiming(targetX, { duration: swipeDuration }, (isFinished) => {
              if (isFinished) {
                runOnJS(processSwipeCompletion)(swipeDirection, swipedItem);
              } else {
                runOnJS(resetActiveCard)();
              }
            });

            // Add final rotation and reset Y position
            activeCardRotate.value = withTiming((swipeDirection === 'right' ? 1 : -1) * 30, { duration: swipeDuration });
            activeCardTranslateY.value = withTiming(0, { duration: swipeDuration });
            return;
          }
        }

        // No significant swipe detected - return to center
        console.log('[useSwipeGestures] No significant swipe detected. Returning to center.');
        runOnJS(resetActiveCard)();

      } catch (err) {
        console.error("[useSwipeGestures] Error in swipe onEnd:", err);
        runOnJS(resetActiveCard)();
      }
    }), [currentIndex, items.length, processSwipeCompletion, resetActiveCard]);

  // Animated style for top card
  const topCardStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { rotate: `${rotate.value}deg` },
    ],
  }));

  return {
    panGesture,
    topCardStyle,
    resetCardAnimations,
    cardAnimations
  };
};
