import React, { useCallback, useEffect } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, PixelRatio, Animated as RNAnimated } from 'react-native';
import ClothingCard from './ClothingCard';
import { isCustomCategory } from '../utils/feedHelpers';
import { defaultCategories, PRELOAD_THRESHOLD } from '../utils/feedConstants';
import { useSwipeGestures } from '../hooks/useSwipeGestures';
import { useSwipeAnimations } from '../hooks/useSwipeAnimations';
import { styles } from './CardStack.styles';

const CardStack = ({
  items,
  loading,
  error,
  noMoreItems,
  currentIndex,
  setCurrentIndex,
  isCurrentItemWishlisted,
  currentUserId,
  onItemPress,
  onWishlistToggle,
  onAddToCart,
  onRefresh,
  activeCategory,
  setActiveCategory,
  onSwipeComplete,
  fetchClothingItems,
  backgroundLoading
}) => {
  // Initialize swipe gestures and animations
  const handleSwipeComplete = useCallback(async (direction, swipedItem) => {
    try {
      if (!swipedItem) {
        console.log("[CardStack] No item to process for swipe completion");
        return;
      }

      console.log(`[CardStack] Processing swipe completion for item ${swipedItem.id}, direction: ${direction}`);

      // Update current index immediately to ensure new card becomes active
      // The animation reset in useSwipeGestures happens synchronously before this
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);

      // Call the parent's swipe completion handler
      if (onSwipeComplete) {
        await onSwipeComplete(direction, swipedItem);
      }

      // Check if we need to load more items
      if (items.length - nextIndex <= PRELOAD_THRESHOLD && !noMoreItems && !backgroundLoading) {
        console.log(`[CardStack] Approaching end, loading more...`);
        if (fetchClothingItems) {
          fetchClothingItems(false, true);
        }
      }
    } catch (error) {
      console.error('[CardStack] Error in handleSwipeComplete:', error);
    }
  }, [currentIndex, setCurrentIndex, onSwipeComplete, items.length, noMoreItems, backgroundLoading, fetchClothingItems]);

  const { panGesture, topCardStyle, resetCardAnimations } = useSwipeGestures({
    items,
    currentIndex,
    onSwipeComplete: handleSwipeComplete
  });

  const {
    likeAnimationOpacity,
    dislikeAnimationOpacity,
    likeAnimationScale,
    dislikeAnimationScale
  } = useSwipeAnimations();

  // Motivational quotes for loading screen
  const loadingQuotes = [
    "Good things take time... ✨",
    "Great style is worth the wait 👗",
    "Perfect outfits are being curated for you 💫",
    "Amazing finds are loading... 🌟",
    "Your next favorite outfit is almost ready! 💕",
    "Style is eternal, patience is rewarded 🎨",
    "The best things in life take a moment longer 🌸",
    "Crafting your perfect wardrobe... 👑",
    "Every great outfit starts with a great selection 🦋",
    "Loading fashion magic just for you! ✨",
    "Quality over quantity - we're choosing the best 💎",
    "Your style journey is about to begin 🌺",
    "Patience is the companion of wisdom... and great style! 🌙",
    "Beautiful things happen to those who wait 🌻",
    "Loading your next style inspiration... 🎭",
    "The perfect outfit is worth every second ⏰",
    "Style is not what you have, it's what you're about to discover 🔍",
    "Great fashion moments are being prepared... 🎪",
    "Your wardrobe adventure is loading... 🗺️",
    "Loading items that will make you feel amazing! 💃",
    "Style is a way to say who you are without speaking... getting ready to speak! 🎤",
    "Fashion fades, but style is eternal... loading eternal style! ♾️",
    "Loading outfits that will boost your confidence... 💪",
    "Every outfit tells a story - yours is loading... 📖",
    "Style is about being yourself, but on purpose... loading purpose! 🎯",
    "Good style happens when preparation meets opportunity 🤝",
    "Loading clothes that will make you feel like the main character 🌟",
    "Your style evolution is about to continue... 🦋",
    "Loading outfits that match your energy! ⚡",
    "Style is the reflection of your attitude... loading attitude! 😎",
    "Fashion is art, and you are the canvas... preparing the masterpiece! 🎨",
    "Loading pieces that will complete your vibe... 🌈",
    "Your next style era is loading... 🔄",
    "Loading clothes that will make you feel unstoppable! 🚀",
    "Style is confidence you can wear... loading confidence! 💫"
  ];

  // State for current quote
  const [currentQuote, setCurrentQuote] = React.useState('');

  // Quote rotation effect
  useEffect(() => {
    if (loading) {
      // Set initial random quote
      const randomQuote = loadingQuotes[Math.floor(Math.random() * loadingQuotes.length)];
      setCurrentQuote(randomQuote);

      // Change quote every 5 seconds
      const quoteInterval = setInterval(() => {
        const randomQuote = loadingQuotes[Math.floor(Math.random() * loadingQuotes.length)];
        setCurrentQuote(randomQuote);
      }, 5000);

      return () => clearInterval(quoteInterval);
    }
  }, [loading]);

  // Note: Animation reset is now handled automatically in useSwipeGestures
  // No need for manual reset on currentIndex change

  // Show loading state
  if (loading) {
    return (
      <View style={styles.statusContainer}>
        <ActivityIndicator size="large" color="#FF6B6B" />
        <Text style={styles.loadingText}>Loading amazing items...</Text>
        <Text style={styles.quoteText}>{currentQuote}</Text> {/* Display current quote */}
      </View>
    );
  }

  // Show error state
  if (error) {
    return (
      <View style={styles.statusContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={onRefresh}
        >
          <Text style={styles.refreshButtonText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Show empty state
  if (!items || items.length === 0 || currentIndex >= items.length) {
    const isCustomCat = isCustomCategory(activeCategory, defaultCategories);

    return (
      <View style={styles.statusContainer}>
        <Text style={styles.emptyText}>
          {noMoreItems
            ? (isCustomCat
              ? `No items found in "${activeCategory}"`
              : "You've seen all items!")
            : "No items available"
          }
        </Text>
        <Text style={styles.emptySubText}>
          {noMoreItems
            ? (isCustomCat
              ? "Try browsing other categories or check back later for new items."
              : "Check back later for new items!")
            : "Please try again later"
          }
        </Text>

        <View style={styles.emptyButtonsContainer}>
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={onRefresh}
          >
            <Text style={styles.refreshButtonText}>Refresh Feed</Text>
          </TouchableOpacity>

          {isCustomCat && (
            <TouchableOpacity
              style={[styles.refreshButton, { marginLeft: 10, backgroundColor: '#4CAF50' }]}
              onPress={() => setActiveCategory('All')}
            >
              <Text style={styles.refreshButtonText}>View All Items</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  }

  // Render card stack
  const visibleCards = 3;
  const cardsToRender = items.slice(currentIndex, currentIndex + visibleCards);

  // Ensure we have cards to render
  if (!cardsToRender.length) {
    return null;
  }

  return (
    <>
      <View style={styles.cardAreaContainer}>
        {cardsToRender.map((item, stackIndex) => {
          const isTopCard = stackIndex === 0;

          // Simple static style for non-top cards (no complex animations)
          let animatedCardStyle = null;
          if (!isTopCard) {
            const scaleDecrement = 0.03;
            const positionOffset = 15;
            animatedCardStyle = {
              transform: [
                { scale: 1 - scaleDecrement * stackIndex },
                { translateY: positionOffset * stackIndex }
              ],
              opacity: 1
            };
          }

          return (
            <ClothingCard
              key={item.id}
              item={item}
              isTopCard={isTopCard}
              isCurrentItemWishlisted={isTopCard ? isCurrentItemWishlisted : false}
              currentUserId={currentUserId}
              onPress={onItemPress}
              onWishlistToggle={onWishlistToggle}
              panGesture={isTopCard ? panGesture : null}
              animatedStyle={isTopCard ? topCardStyle : animatedCardStyle}
              style={[
                styles.card,
                {
                  zIndex: visibleCards - stackIndex
                }
              ]}
            />
          );
        })}
      </View>

      {/* Render animations */}
      <View style={styles.animationsContainer}>
        {/* Like Animation */}
        <RNAnimated.View
          style={[
            styles.animationContainer,
            {
              opacity: likeAnimationOpacity,
              transform: [{ scale: likeAnimationScale }],
            }
          ]}
          pointerEvents="none"
        >
          <Text style={styles.animationText}>❤️</Text>
        </RNAnimated.View>

        {/* Dislike Animation */}
        <RNAnimated.View
          style={[
            styles.animationContainer,
            {
              opacity: dislikeAnimationOpacity,
              transform: [{ scale: dislikeAnimationScale }],
            }
          ]}
          pointerEvents="none"
        >
          <Text style={styles.animationText}>👎</Text>
        </RNAnimated.View>


      </View>
    </>
  );
};

export default CardStack;
