import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated from 'react-native-reanimated';
import { GestureDetector } from 'react-native-gesture-handler';
import { getLowQualityImageUrl } from '../utils/imageUtils';
import { formatPrice } from '../utils/feedHelpers';
import { StockBadge } from './StockDisplay';
import { styles } from './ClothingCard.styles';

const ClothingCard = ({
  item,
  isTopCard,
  isCurrentItemWishlisted,
  currentUserId,
  onPress,
  onWishlistToggle,
  style,
  panGesture,
  animatedStyle
}) => {
  const handlePress = () => {
    if (isTopCard && onPress) {
      onPress(item);
    }
  };

  const handleWishlistPress = (event) => {
    // Prevent event propagation to avoid triggering card press
    if (event) {
      event.stopPropagation();
    }

    console.log(`[ClothingCard] Bookmark button pressed for item ${item.id}, isTopCard: ${isTopCard}, currentUserId: ${currentUserId}`);

    if (isTopCard && currentUserId && onWishlistToggle) {
      console.log(`[ClothingCard] Calling onWishlistToggle for item ${item.id}`);
      onWishlistToggle();
    } else {
      console.log(`[ClothingCard] Bookmark press ignored - isTopCard: ${isTopCard}, currentUserId: ${currentUserId}, onWishlistToggle: ${!!onWishlistToggle}`);
    }
  };
  const cardContent = (
    <Animated.View style={[styles.card, style, animatedStyle]}>
      <TouchableOpacity
        activeOpacity={0.85}
        style={styles.cardContent}
        onPress={handlePress}
        disabled={!isTopCard}
      >
        <Image
          source={{ uri: getLowQualityImageUrl(item.imageUrl) }}
          style={styles.image}
          resizeMode="cover"
          fadeDuration={0}
        />

        {/* Stock Badge */}
        <StockBadge stock={item.stock || 0} />

        <View style={styles.overlay}>
          <View style={styles.itemInfoContainer}>
            <Text style={styles.itemTitle}>
              {item.title || 'Untitled'}
            </Text>

            {item.brand && (
              <Text style={styles.itemBrand}>
                {item.brand}
              </Text>
            )}

            {item.price && (
              <Text style={styles.itemPrice}>
                {formatPrice(item.price)}
              </Text>
            )}
          </View>
        </View>
      </TouchableOpacity>

      {/* Bookmark button positioned absolutely outside the main TouchableOpacity */}
      <TouchableOpacity
        style={styles.bookmarkButton}
        onPress={handleWishlistPress}
        disabled={!isTopCard || !currentUserId}
        activeOpacity={0.7}
      >
        <Ionicons
          name={isTopCard && isCurrentItemWishlisted ? "bookmark" : "bookmark-outline"}
          size={28}
          color="#fff"
        />
      </TouchableOpacity>
    </Animated.View>
  );

  // Wrap the top card with GestureDetector, use regular View for others
  return isTopCard && panGesture ? (
    <GestureDetector key={`gesture-${item.id}`} gesture={panGesture}>
      {cardContent}
    </GestureDetector>
  ) : (
    cardContent
  );
};

export default ClothingCard;
